// src/contexts/ThemeContext.jsx (or ThemeContext.tsx)
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

const ThemeContext = createContext(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// --- Theme Definitions for each Brand ---
// Define CSS variables and MUI palette properties for each brand here.
// IMPORTANT: Palette colors must be concrete hex/rgb values for MUI's internal calculations.
// Other CSS variables can be defined as strings to be injected.
const BRAND_THEMES_DATA = {
  // --- My Company (Default) ---
  myCompany: {
    name: 'My Company',
    cssVariables: `
      /* My Company's Primary Palette */
      --color-primary-brand: #0F5FDC; /* Dark Blue */
      --color-secondary-brand: #2ECCCB; /* Software Teal */
      --color-accent-brand: #3C91FF; /* Software Blue */

      /* Text Colors for My Company */
      --color-text-default-brand: #000032; /* Midnight Blue */
      --color-text-secondary-brand: #17707F; /* Dark Teal */
      --color-text-muted-brand: #8CC8FA; /* Mid Blue */
      --color-text-link-brand: #0F5FDC;
      --color-text-link-hover-brand: #3C91FF;

      /* Backgrounds for My Company */
      --theme-background-primary-brand: #ECF3F8; /* Tech Grey */
      --theme-background-secondary-brand: #FFFFFF; /* White */
      --theme-border-color-brand: #DCE6F0; /* Light Blue */

      /* Buttons for My Company */
      --button-primary-bg-brand: #2ECCCB; /* Software Teal */
      --button-primary-hover-bg-brand: #23A3AD; /* Software Teal AA */
      --button-secondary-bg-brand: #0F5FDC; /* Dark Blue */
      --button-color-brand: #FFFFFF;



       --button-add:rgb(143, 48, 48);
       --button-add-hover: #23A3AD;
      --button-ok:#007a87;
      --button-ok-hover: #23A3AD;
      --button-cancel:  #007a87;
      --button-cancel-hover: #23A3AD;
      --button-edit: #007a87;
      --button-edit-hover: #23A3AD;
      --button-approve: #007a87;
      --button-approve-hover: #23A3AD;
      --button-reject: #007a87;
      --button-reject-hover: #23A3AD;
      --button-delete: #007a87;
      --button-delete-hover: #23A3AD;

      /* Rating*/ 
       --color-rating-star :  #007a87;

      /* CHECK box */
      --checkbox: #007a87;
  /Slider/
    --SliderWidth:300px;

    /speeddail/
    speeddial-container {
    height: var(--speeddial-container-height);
    position: relative;
    z-index: 0;
    margin-bottom: var(--spacing-lg);
  }


      /* Cards for My Company */
      --card-shadow-brand: 0 4px 6px rgba(0, 0, 0, 0.1);
      --card-border-radius-brand: 8px;

      /* Navbar/Footer */
      --navbar-bg-brand: #0F5FDC; /* Dark Blue */
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #ECF3F8; /* Tech Grey */
      --footer-text-color-brand: #17707F; /* Dark Teal */

      /* Mapping generic variables to brand-specific ones */
      --color-software-teal: var(--color-secondary-brand);
      --color-software-teal-aa: #23A3AD; /* Keeping universal if not overridden by brand */
      --color-dark-blue: var(--color-primary-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;
      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9; /* Universal if not overridden */
      --color-light-teal: #A4F4FF; /* Universal if not overridden */
      --color-software-blue: var(--color-accent-brand);
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000032;
      --alert-success-bg: #000000;
      --alert-success-color: #ffffff;
      /* Status Colors (universal in this example, but can be brand-specific) */
      --color-status-success: #28a745;
      --color-status-info: #17a2b8;
      --color-status-warning: #ffc107;
      --color-status-error: #dc3545;

      --color-status-success-background: #d4edda;
      --color-status-info-background: #d1ecf1;
      --color-status-warning-background: #fff3cd;
      --color-status-error-background: #f8d7da;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-padding: 1rem;
      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-height: 60px;
      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      /* Typography */
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-bold: 700;
      --font-family-primary: 'HCLTech Roobert', Roboto, sans-serif; /* Your company's specific font */
      --font-size-base: 1rem;
      --line-height-body: 1.5rem; /* 24px */

      /* Spacing */
      --spacing-xs: 0.25rem;
      --spacing-sm: 0.5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;

      /* Radius */
      --radius-sm: calc(var(--card-border-radius-brand) / 2);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.5);

      /* Input */
      --input-padding: 0.5rem;
      --input-border-thickness: 1px;
      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      /* Transitions */
      --transition-fast: 0.2s ease-in-out;
      --transition-medium: 0.4s ease-in-out;
      --transition-ease-in-out: all 0.3s ease-in-out;
    `,
    // MUI Palette configuration (must use concrete color values)
    muiPalette: {
      primary: { main: '#0F5FDC', light: '#3C91FF', dark: '#000032', contrastText: '#FFFFFF' },
      secondary: { main: '#2ECCCB', light: '#A4F4FF', dark: '#17707F', contrastText: '#000000' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#ECF3F8', paper: '#FFFFFF' },
      text: { primary: '#000032', secondary: '#17707F', disabled: '#8CC8FA' },
    },
    // Fonts specific to this brand (if different from default HCLTech Roobert)
    fonts: `
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/Roobert-Medium.woff2') format('woff2');
        font-weight: 500;
        font-style: normal;
      }
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/Roobert-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  
 
};

export const ThemeProvider = ({ children }) => {
  // Hardcode the brand key to 'myCompany' as per the request
  const currentBrandKey = 'myCompany';

  const injectThemeStyles = useCallback((brandKey) => {
    const brandData = BRAND_THEMES_DATA[brandKey];
    if (!brandData) {
      console.warn(`Brand "${brandKey}" data not found.`);
      return;
    }

    let styleTag = document.getElementById('brand-theme-styles');
    if (!styleTag) {
      styleTag = document.createElement('style');
      styleTag.id = 'brand-theme-styles';
      document.head.appendChild(styleTag);
    }

    let fontStyleTag = document.getElementById('brand-theme-fonts');
    if (!fontStyleTag) {
      fontStyleTag = document.createElement('style');
      fontStyleTag.id = 'brand-theme-fonts';
      document.head.appendChild(fontStyleTag);
    }

    // Inject CSS variables for the current brand
    styleTag.textContent = `:root { ${brandData.cssVariables} }`;

    // Inject @font-face rules for the current brand
    fontStyleTag.textContent = brandData.fonts || ''; // Use empty string if no fonts defined
    
    // Set data-theme attribute on body if you still want to leverage it for very specific overrides
    // document.body.setAttribute('data-theme', brandKey); // Optional, if you still want to use it
  }, []);

  // Call injectThemeStyles directly for the default theme
  useEffect(() => {
    injectThemeStyles(currentBrandKey);
  }, [currentBrandKey, injectThemeStyles]);

  // setBrand and brandOptions are no longer needed for dynamic selection
  // but getMuiPaletteConfig is still used by App.jsx
  const getMuiPaletteConfig = useCallback(() => {
    return BRAND_THEMES_DATA[currentBrandKey]?.muiPalette;
  }, [currentBrandKey]);

  return (
    <ThemeContext.Provider value={{ currentBrandKey, getMuiPaletteConfig }}>
      {children}
    </ThemeContext.Provider>
  );
};
import React from 'react';

// A reusable Box component for consistent styling
const Box = ({ children, className = '' }) => (
  <div className={`bg-white border-2 border-black ${className}`}>
    {children}
  </div>
);

// Main App component that lays out the entire wireframe
export default function App() {
  return (
    <div className="bg-gray-100 p-2.5 h-screen flex gap-2.5">

      {/* Left Sidebar */}
      <aside className="w-1/4">
        <Box className="h-full" />
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col gap-2.5">

        {/* Top Bar */}
        <header>
          <Box className="h-12" />
        </header>

        {/* Middle Content Section */}
        <div className="flex-1 flex gap-2.5">

          {/* Center Content Column */}
          <div className="flex-1 flex flex-col gap-2.5">

            {/* Large Box */}
            <Box className="flex-[3_3_0%]" />

            {/* Three Small Boxes */}
            <Box className="flex-1" />
            <Box className="flex-1" />
            <Box className="flex-1" />
          </div>

          {/* Right Sidebar */}
          <aside className="w-1/3 flex flex-col gap-2.5">

            {/* Grid of 6 boxes */}
            <div className="flex-[2_2_0%] grid grid-cols-3 grid-rows-2 gap-2.5">
              <Box />
              <Box />
              <Box />
              <Box />
              <Box />
              <Box />
            </div>

            {/* Bottom Box in Right Sidebar */}
            <Box className="flex-1" />
          </aside>
        </div>

        {/* Bottom Bar Section */}
        <footer className="h-32 flex gap-2.5">
          <Box className="flex-1" />
          <Box className="flex-1" />
          <Box className="flex-1" />
        </footer>

      </main>
    </div>
  );
}
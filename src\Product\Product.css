/* Product Page Styles */
.product-page {
  min-height: 100vh;
  background-color: var(--theme-background-primary);
  font-family: var(--font-family-primary);
  padding: var(--spacing-md);
}

/* Header */
.product-header {
  height: 50px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
  margin-bottom: var(--spacing-md);
}

/* Main Content */
.product-main {
  max-width: 1200px;
  margin: 0 auto;
}

.product-container {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 2px solid var(--color-text-default);
  padding: var(--spacing-md);
  background-color: var(--theme-background-secondary);
}

/* Left Side - Main Image */
.product-image-section {
  flex: 2;
}

.main-product-image {
  width: 100%;
  height: 400px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Right Side - Details */
.product-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* Top Info Bar */
.product-info-bar {
  height: 40px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Right Content Container */
.right-content {
  display: flex;
  gap: var(--spacing-sm);
  flex-grow: 1;
}

/* Thumbnails Grid */
.product-thumbnails {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: var(--spacing-xs);
  flex: 1;
}

.thumbnail-item {
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
  aspect-ratio: 1;
}

/* Large Info Section */
.product-large-info {
  flex: 1;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Bottom Section */
.product-bottom-section {
  display: flex;
  gap: var(--spacing-md);
}

.product-specs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.spec-item {
  height: 80px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.product-additional-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-section {
  height: 120px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-container {
    flex-direction: column;
  }

  .right-content {
    flex-direction: column;
  }

  .product-thumbnails {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }

  .product-bottom-section {
    flex-direction: column;
  }
}
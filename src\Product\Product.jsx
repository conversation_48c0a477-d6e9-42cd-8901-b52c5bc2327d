import React from 'react';
import './Product.css';

const Product = () => {
  return (
    <div className="product-page">
      {/* Header/Navigation */}
      <header className="product-header">
        <div className="header-content">
          <div className="logo-section">Logo</div>
          <nav className="navigation">
            <span>Navigation</span>
          </nav>
          <div className="header-actions">
            <span>Actions</span>
          </div>
        </div>
      </header>

      {/* Main Product Content */}
      <main className="product-main">
        <div className="product-container">
          {/* Left Side - Main Product Image */}
          <div className="product-image-section">
            <div className="main-product-image">
              <div className="image-placeholder">Main Product Image</div>
            </div>
          </div>

          {/* Right Side - Product Details and Thumbnails */}
          <div className="product-details-section">
            {/* Product Info */}
            <div className="product-info">
              <div className="info-placeholder">Product Details</div>
            </div>

            {/* Product Thumbnails Grid */}
            <div className="product-thumbnails">
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 1</div>
              </div>
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 2</div>
              </div>
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 3</div>
              </div>
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 4</div>
              </div>
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 5</div>
              </div>
              <div className="thumbnail-item">
                <div className="thumbnail-placeholder">Thumb 6</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Additional Product Information */}
        <div className="product-bottom-section">
          {/* Left Column - Product Specifications */}
          <div className="product-specs">
            <div className="spec-item">
              <div className="spec-placeholder">Specification 1</div>
            </div>
            <div className="spec-item">
              <div className="spec-placeholder">Specification 2</div>
            </div>
            <div className="spec-item">
              <div className="spec-placeholder">Specification 3</div>
            </div>
          </div>

          {/* Right Column - Additional Information */}
          <div className="product-additional-info">
            <div className="info-section">
              <div className="info-placeholder">Additional Information 1</div>
            </div>
            <div className="info-section">
              <div className="info-placeholder">Additional Information 2</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Procduct;
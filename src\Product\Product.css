/* Product Page Styles */
.product-page {
  min-height: 100vh;
  background-color: var(--theme-background-primary);
  font-family: var(--font-family-primary);
}

/* Header */
.product-header {
  height: 60px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.header-content {
  width: 100%;
  height: 100%;
  border: 1px solid var(--color-text-default);
}

/* Main Content */
.product-main {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.product-container {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

/* Left Side - Main Image */
.product-image-section {
  flex: 2;
}

.main-product-image {
  width: 100%;
  aspect-ratio: 4/3;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Right Side - Details */
.product-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.product-info {
  height: 120px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Thumbnails Grid */
.product-thumbnails {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
  flex-grow: 1;
}

.thumbnail-item {
  aspect-ratio: 1;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Bottom Section */
.product-bottom-section {
  display: flex;
  gap: var(--spacing-lg);
}

.product-specs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.spec-item {
  height: 80px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.product-additional-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-section {
  height: 120px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-container {
    flex-direction: column;
  }

  .product-bottom-section {
    flex-direction: column;
  }
}
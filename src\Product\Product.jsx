import React from 'react';
import './Product.css';

const Product = () => {
  return (
    <div className="product-page">
      {/* Header */}
      <header className="product-header"></header>

      {/* Main Content */}
      <main className="product-main">
        <div className="product-container">
          {/* Left Side - Main Image */}
          <div className="product-image-section">
            <div className="main-product-image"></div>
          </div>

          {/* Right Side - Details and Thumbnails */}
          <div className="product-details-section">
            {/* Top Info Bar */}
            <div className="product-info-bar"></div>

            <div className="right-content">
              {/* Thumbnails Grid */}
              <div className="product-thumbnails">
                <div className="thumbnail-item"></div>
                <div className="thumbnail-item"></div>
                <div className="thumbnail-item"></div>
                <div className="thumbnail-item"></div>
                <div className="thumbnail-item"></div>
                <div className="thumbnail-item"></div>
              </div>

              {/* Large Info Section */}
              <div className="product-large-info"></div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="product-bottom-section">
          {/* Left Column */}
          <div className="product-specs">
            <div className="spec-item"></div>
            <div className="spec-item"></div>
            <div className="spec-item"></div>
          </div>

          {/* Right Column */}
          <div className="product-additional-info">
            <div className="info-section"></div>
            <div className="info-section"></div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Product;
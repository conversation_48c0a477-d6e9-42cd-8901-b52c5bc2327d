/* Basic Reset & Setup */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* General Box Styling */
.box {
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Main Wireframe Container */
.wireframe-container {
  display: flex;
  height: 100vh;
  padding: 10px;
  gap: 10px;
  background-color: var(--theme-background-primary);
  font-family: var(--font-family-primary);
}

/* Left Sidebar */
.left-sidebar {
  width: 25%;
}

/* Main Content Area */
.main-content-area {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 10px;
}

/* Top Bar */
.top-bar {
  height: 50px;
}

/* Middle Content Area */
.middle-content {
  display: flex;
  flex-grow: 1;
  gap: 10px;
}

/* Center Content */
.center-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 10px;
}

/* Large Box */
.large-box {
  flex-grow: 3;
}

/* Small Boxes */
.small-box {
  flex-grow: 1;
}

/* Right Sidebar */
.right-sidebar {
  display: flex;
  flex-direction: column;
  width: 35%;
  gap: 10px;
}

/* Grid Container */
.grid-container {
  flex-grow: 2;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
}

/* Bottom Right Box */
.bottom-right-box {
  flex-grow: 1;
}

/* Bottom Bar */
.bottom-bar {
  display: flex;
  height: 120px;
  gap: 10px;
}

/* Bottom Boxes */
.bottom-box {
  flex-grow: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .wireframe-container {
    flex-direction: column;
  }

  .left-sidebar {
    width: 100%;
    height: 100px;
  }

  .middle-content {
    flex-direction: column;
  }

  .right-sidebar {
    width: 100%;
  }

  .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }

  .bottom-bar {
    flex-direction: column;
    height: auto;
  }
}
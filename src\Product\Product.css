/* Product Page Styles */
.product-page {
  min-height: 100vh;
  background-color: var(--theme-background-primary);
  font-family: var(--font-family-primary);
  color: var(--color-text-default);
}

/* Header Styles */
.product-header {
  background-color: var(--navbar-bg);
  color: var(--navbar-text-color);
  height: var(--navbar-height);
  border-bottom: 2px solid var(--color-text-default);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section,
.navigation,
.header-actions {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm);
  border: 1px solid var(--navbar-text-color);
  background-color: rgba(255, 255, 255, 0.1);
}

.logo-section {
  justify-content: flex-start;
}

.header-actions {
  justify-content: flex-end;
}

/* Main Content */
.product-main {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.product-container {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

/* Left Side - Main Product Image */
.product-image-section {
  flex: 2;
}

.main-product-image {
  width: 100%;
  aspect-ratio: 4/3;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

/* Right Side - Product Details */
.product-details-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.product-info {
  height: 120px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.info-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

/* Product Thumbnails Grid */
.product-thumbnails {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
  flex-grow: 1;
}

.thumbnail-item {
  aspect-ratio: 1;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
}

/* Bottom Section */
.product-bottom-section {
  display: flex;
  gap: var(--spacing-lg);
}

.product-specs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.spec-item {
  height: 80px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

.spec-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.product-additional-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.info-section {
  height: 120px;
  border: 2px solid var(--color-text-default);
  background-color: var(--theme-background-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-container {
    flex-direction: column;
  }

  .product-bottom-section {
    flex-direction: column;
  }

  .header-content {
    padding: 0 var(--spacing-sm);
  }

  .logo-section,
  .navigation,
  .header-actions {
    font-size: 0.8rem;
  }
}